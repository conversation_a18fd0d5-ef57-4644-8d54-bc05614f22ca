import asyncio
import logging
import requests
import json
import os
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
from binance.client import Client
from binance.exceptions import Binance<PERSON>IException
import uuid
import qrcode
from io import BytesIO
from datetime import datetime, timedelta
import hashlib
import hmac
import time
import re

# ================================
# 🔑 API CONFIGURATION - EDIT THESE VALUES FIRST!
# ================================

# Telegram Bot Token (get from @BotFather)
TELEGRAM_TOKEN = "**********************************************"

# Binance API Keys (get from Binance API Management)
BINANCE_API_KEY = "fWNu1xqw9hCg8DUtAYuE3Tf35qt4ZFVVBMYwuU5dj8PaNyCRDrAzP2daF8QdSJ6s"
BINANCE_SECRET_KEY = "5poWiodXlLJFfMRSDtJXcNWpnAOEWrPYOr2dtICwpGatizaa20vMx5CXjWmZ3yVr"

# SMM Panel API Key (get from your chhean-smm.net admin panel)
SMM_API_KEY = "7uceg5rz516srjd2o4my0bl5ws9tcc1z6w0dchsusmb50mc3cz3oyih5ywc7xq0j"

# Your Binance ID (8-digit number from Binance Profile > Security)
BINANCE_ID = "733685808"

# Telegram Group ID for logging payments (get from @userinfobot)
# Example: -************* (include the negative sign for groups)
LOG_GROUP_ID = "-1002581027304"  # Replace with your actual group ID

# ================================
# ⚠️  SECURITY WARNING: 
# Replace the values above with your real API keys
# Never share this file with anyone after adding your keys!
# ================================

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MemoryStorageManager:
    def __init__(self):
        """Initialize in-memory storage"""
        self.payments = {}  # payment_id -> payment_data
        self.used_orders = set()  # Set of used order IDs
        self.user_sessions = {}  # user_id -> session_data
        self.user_payments = {}  # user_id -> list of payment_ids
        logger.info("Memory storage initialized successfully")

    def save_payment(self, payment_data):
        """Save payment to memory"""
        try:
            payment_id = payment_data['payment_id']
            self.payments[payment_id] = payment_data.copy()

            # Track user payments
            user_id = payment_data['user_id']
            if user_id not in self.user_payments:
                self.user_payments[user_id] = []
            self.user_payments[user_id].append(payment_id)

            logger.info(f"Payment saved: {payment_id}")
            return True
        except Exception as e:
            logger.error(f"Error saving payment: {e}")
            return False

    def get_payment(self, payment_id):
        """Get payment by ID"""
        return self.payments.get(payment_id)

    def update_payment_status(self, payment_id, status, order_id=None, completed_at=None, smm_result=None):
        """Update payment status"""
        try:
            if payment_id in self.payments:
                self.payments[payment_id]['status'] = status
                if order_id:
                    self.payments[payment_id]['order_id'] = order_id
                if completed_at:
                    self.payments[payment_id]['completed_at'] = completed_at
                if smm_result:
                    self.payments[payment_id]['smm_result'] = smm_result
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating payment status: {e}")
            return False

    def is_order_used(self, order_id):
        """Check if order ID is already used"""
        return order_id in self.used_orders

    def mark_order_used(self, order_id, payment_id, user_id):
        """Mark order ID as used"""
        try:
            self.used_orders.add(order_id)
            logger.info(f"Order marked as used: {order_id}")
            return True
        except Exception as e:
            logger.error(f"Error marking order as used: {e}")
            return False

    def get_user_payments(self, user_id, limit=10):
        """Get user's payment history"""
        try:
            if user_id not in self.user_payments:
                return []

            payment_ids = self.user_payments[user_id][-limit:]  # Get last N payments
            payments = []
            for payment_id in reversed(payment_ids):  # Most recent first
                if payment_id in self.payments:
                    payments.append(self.payments[payment_id])
            return payments
        except Exception as e:
            logger.error(f"Error getting user payments: {e}")
            return []

    def get_pending_payments(self):
        """Get all pending payments"""
        try:
            pending = []
            for payment in self.payments.values():
                if payment.get('status') == 'pending':
                    pending.append(payment)
            return pending
        except Exception as e:
            logger.error(f"Error getting pending payments: {e}")
            return []

    def cleanup_expired_payments(self):
        """Clean up expired payments"""
        try:
            current_time = datetime.now()
            expired_count = 0

            for payment_id, payment in self.payments.items():
                if payment.get('status') == 'pending':
                    expires_at = datetime.fromisoformat(payment['expires_at'])
                    if current_time > expires_at:
                        payment['status'] = 'expired'
                        expired_count += 1

            if expired_count > 0:
                logger.info(f"Marked {expired_count} payments as expired")
            return expired_count
        except Exception as e:
            logger.error(f"Error cleaning up expired payments: {e}")
            return 0

class SMMBinancePaymentBot:
    def __init__(self, telegram_token, binance_api_key, binance_secret_key, smm_api_key, binance_id, log_group_id):
        self.telegram_token = telegram_token
        self.log_group_id = log_group_id
        
        # Initialize memory storage
        self.db = MemoryStorageManager()
        
        # Initialize Binance client with timestamp offset to fix time sync issues
        self.binance_client = Client(
            binance_api_key, 
            binance_secret_key,
            requests_params={'timeout': 30}
        )
        
        # Try to sync server time
        try:
            server_time = self.binance_client.get_server_time()
            time_offset = server_time['serverTime'] - int(time.time() * 1000)
            self.binance_client.timestamp_offset = time_offset
            logger.info(f"Binance time offset set to: {time_offset}ms")
        except Exception as e:
            logger.warning(f"Could not sync Binance server time: {e}")
            
        self.smm_api_key = smm_api_key
        self.smm_api_url = "https://chhean-smm.net/adminapi/v1"
        self.binance_id = binance_id
        
        # Clean up expired payments on startup
        self.db.cleanup_expired_payments()

    def generate_payment_id(self):
        """Generate unique payment ID"""
        return f"PAY{uuid.uuid4().hex[:8].upper()}"

    async def verify_binance_transaction(self, order_id, expected_amount, expected_asset='USDT'):
        """Verify if a Binance transaction is real and matches expected criteria"""
        try:
            # Clean and validate order ID format
            order_id = str(order_id).strip()

            # Check if it looks like a valid Binance order ID (numeric or alphanumeric)
            if not re.match(r'^[A-Za-z0-9]{8,}$', order_id):
                return {
                    'valid': False,
                    'error': 'Invalid order ID format. Must be alphanumeric and at least 8 characters.'
                }

            # Try to get deposit history first (most common for P2P payments)
            try:
                deposits = self.binance_client.get_deposit_history(
                    asset=expected_asset,
                    limit=100  # Check last 100 deposits
                )

                # Check if order ID matches any recent deposit
                for deposit in deposits:
                    if (deposit.get('txId') == order_id or
                        deposit.get('id') == order_id or
                        str(deposit.get('insertTime')) == order_id):

                        deposit_amount = float(deposit.get('amount', 0))
                        if abs(deposit_amount - expected_amount) < 0.01:  # Allow small rounding differences
                            return {
                                'valid': True,
                                'transaction_type': 'deposit',
                                'amount': deposit_amount,
                                'asset': deposit.get('asset'),
                                'status': deposit.get('status'),
                                'tx_id': deposit.get('txId'),
                                'timestamp': deposit.get('insertTime')
                            }
                        else:
                            return {
                                'valid': False,
                                'error': f'Amount mismatch. Expected: {expected_amount} {expected_asset}, Found: {deposit_amount} {deposit.get("asset")}'
                            }

            except Exception as deposit_error:
                logger.warning(f"Could not check deposit history: {deposit_error}")

            # Try to get trade history (for spot trading)
            try:
                # Check recent trades for the asset pair
                symbol = f"{expected_asset}USDT" if expected_asset != 'USDT' else 'BTCUSDT'
                trades = self.binance_client.get_my_trades(symbol=symbol, limit=100)

                for trade in trades:
                    if (str(trade.get('orderId')) == order_id or
                        str(trade.get('id')) == order_id):

                        trade_qty = float(trade.get('qty', 0))
                        if trade.get('isBuyer'):  # If user was buying
                            trade_amount = trade_qty
                        else:  # If user was selling
                            trade_amount = float(trade.get('quoteQty', 0))

                        if abs(trade_amount - expected_amount) < 0.01:
                            return {
                                'valid': True,
                                'transaction_type': 'trade',
                                'amount': trade_amount,
                                'asset': expected_asset,
                                'order_id': trade.get('orderId'),
                                'trade_id': trade.get('id'),
                                'timestamp': trade.get('time')
                            }
                        else:
                            return {
                                'valid': False,
                                'error': f'Amount mismatch. Expected: {expected_amount} {expected_asset}, Found: {trade_amount}'
                            }

            except Exception as trade_error:
                logger.warning(f"Could not check trade history: {trade_error}")

            # Try to check order history
            try:
                # Get all orders for common USDT pairs
                common_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT']

                for symbol in common_symbols:
                    try:
                        orders = self.binance_client.get_all_orders(symbol=symbol, limit=100)

                        for order in orders:
                            if str(order.get('orderId')) == order_id:
                                order_qty = float(order.get('executedQty', 0))
                                order_amount = float(order.get('cummulativeQuoteQty', 0))

                                # Check if amounts match (considering both base and quote amounts)
                                if (abs(order_qty - expected_amount) < 0.01 or
                                    abs(order_amount - expected_amount) < 0.01):
                                    return {
                                        'valid': True,
                                        'transaction_type': 'order',
                                        'amount': order_amount if order_amount > 0 else order_qty,
                                        'asset': expected_asset,
                                        'order_id': order.get('orderId'),
                                        'symbol': symbol,
                                        'status': order.get('status'),
                                        'timestamp': order.get('time')
                                    }
                                else:
                                    return {
                                        'valid': False,
                                        'error': f'Amount mismatch. Expected: {expected_amount} {expected_asset}, Found: {order_amount} (quote) or {order_qty} (base)'
                                    }
                    except Exception:
                        continue  # Try next symbol

            except Exception as order_error:
                logger.warning(f"Could not check order history: {order_error}")

            # If we get here, the transaction was not found
            return {
                'valid': False,
                'error': 'Transaction not found in your Binance account history. Please ensure you provide a valid transaction ID from your recent Binance activity.'
            }

        except BinanceAPIException as e:
            logger.error(f"Binance API error during verification: {e}")
            return {
                'valid': False,
                'error': f'Binance API error: {e.message if hasattr(e, "message") else str(e)}'
            }
        except Exception as e:
            logger.error(f"Unexpected error during transaction verification: {e}")
            return {
                'valid': False,
                'error': f'Verification error: {str(e)}'
            }

    def generate_payment_info_qr(self, amount, payment_id):
        """Generate QR code with payment information as plain text"""
        try:
            # Create payment information that any QR scanner can read
            payment_info = f"""PAYMENT INFO:
Binance Pay ID: {self.binance_id}
Amount: {amount} USDT
Payment ID: {payment_id}

INSTRUCTIONS:
1. Open Binance app
2. Go to Pay section
3. Send to: {self.binance_id}
4. Amount: {amount} USDT
5. Copy Order ID after payment
6. Send Order ID to bot"""

            # Generate QR code with payment information
            qr = qrcode.QRCode(
                version=2,  # Larger version for more text
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=4,
            )
            qr.add_data(payment_info)
            qr.make(fit=True)

            # Create QR code image
            qr_image = qr.make_image(fill_color="black", back_color="white")

            # Save to BytesIO buffer
            qr_buffer = BytesIO()
            qr_image.save(qr_buffer, format='PNG')
            qr_buffer.seek(0)

            return qr_buffer

        except Exception as e:
            logger.error(f"Error generating payment info QR code: {e}")
            return None

    def generate_payment_qr_code(self, amount, payment_id):
        """Generate QR code for Binance payment with contact info"""
        try:
            # Create a simple contact-style QR code that might work better
            # This creates a vCard format that phones can recognize
            contact_info = f"""BEGIN:VCARD
VERSION:3.0
FN:Binance Payment - {amount} USDT
ORG:Binance Pay
NOTE:Send {amount} USDT to Binance ID: {self.binance_id}\\nPayment ID: {payment_id}\\nUse exact amount: {amount} USDT\\nSend Order ID after payment
URL:https://app.binance.com/en/pay
END:VCARD"""

            # Generate QR code
            qr = qrcode.QRCode(
                version=2,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=4,
            )
            qr.add_data(contact_info)
            qr.make(fit=True)

            # Create QR code image
            qr_image = qr.make_image(fill_color="black", back_color="white")

            # Save to BytesIO buffer
            qr_buffer = BytesIO()
            qr_image.save(qr_buffer, format='PNG')
            qr_buffer.seek(0)

            return qr_buffer

        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            return None

    async def create_binance_pay_order(self, amount, payment_id):
        """Create Binance Pay order and get QR code"""
        try:

            # Binance Pay API endpoint
            base_url = "https://bpay.binanceapi.com"
            endpoint = "/binancepay/openapi/v2/order"

            # Create order data
            timestamp = str(int(time.time() * 1000))
            nonce = str(int(time.time() * 1000000))  # microsecond timestamp as nonce

            order_data = {
                "env": {
                    "terminalType": "WEB"
                },
                "merchantTradeNo": payment_id,
                "orderAmount": str(amount),
                "currency": "USDT",
                "goods": {
                    "goodsType": "01",  # Virtual goods
                    "goodsCategory": "Z000",  # Other
                    "referenceGoodsId": "SMM_FUNDS",
                    "goodsName": "SMM Panel Funds",
                    "goodsDetail": f"Add ${amount} USDT to SMM account"
                }
            }

            # Create request body
            body = json.dumps(order_data, separators=(',', ':'))

            # Create signature
            payload = f"{timestamp}\n{nonce}\n{body}\n"
            signature = hmac.new(
                self.binance_secret_key.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha512
            ).hexdigest().upper()

            # Headers
            headers = {
                'Content-Type': 'application/json',
                'BinancePay-Timestamp': timestamp,
                'BinancePay-Nonce': nonce,
                'BinancePay-Certificate-SN': self.binance_api_key,
                'BinancePay-Signature': signature
            }

            # Make request
            response = requests.post(f"{base_url}{endpoint}", headers=headers, data=body)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'SUCCESS':
                    data = result.get('data', {})
                    qr_content = data.get('qrContent')
                    qr_code_url = data.get('qrcodeLink')

                    if qr_content:
                        # Create QR code from the Binance Pay content
                        qr = qrcode.QRCode(
                            version=2,
                            error_correction=qrcode.constants.ERROR_CORRECT_M,
                            box_size=8,
                            border=4,
                        )
                        qr.add_data(qr_content)
                        qr.make(fit=True)

                        qr_image = qr.make_image(fill_color="black", back_color="white")
                        qr_buffer = BytesIO()
                        qr_image.save(qr_buffer, format='PNG')
                        qr_buffer.seek(0)

                        return {
                            'success': True,
                            'qr_buffer': qr_buffer,
                            'qr_content': qr_content,
                            'qr_url': qr_code_url,
                            'order_data': data
                        }

            logger.error(f"Binance Pay API error: {response.text}")
            return {'success': False, 'error': f"API Error: {response.text}"}

        except Exception as e:
            logger.error(f"Error creating Binance Pay order: {e}")
            return {'success': False, 'error': str(e)}

    async def get_binance_pay_qr_code(self, amount, payment_id):
        """Generate payment information QR code"""
        try:
            # Create QR code with payment information
            qr_buffer = self.generate_payment_info_qr(amount, payment_id)
            if qr_buffer:
                logger.info(f"Created payment info QR code for payment {payment_id}")
                return qr_buffer
            else:
                logger.warning("Payment info QR failed, using fallback")
                return self.generate_payment_qr_code(amount, payment_id)

        except Exception as e:
            logger.error(f"Error creating payment QR code: {e}")
            return self.generate_payment_qr_code(amount, payment_id)

    async def send_log_to_group(self, context, message):
        """Send log message to specified group"""
        try:
            if self.log_group_id and self.log_group_id != "-*************":  # Check if real group ID is set
                await context.bot.send_message(
                    chat_id=self.log_group_id,
                    text=message,
                    parse_mode='Markdown'
                )
        except Exception as e:
            logger.error(f"Failed to send log to group: {e}")

    async def add_funds_to_smm(self, username, amount, details):
        """Add funds to SMM panel user account using addpayment endpoint"""
        try:
            payload = {
                'key': self.smm_api_key,
                'action': 'addpayment',
                'username': username,
                'amount': amount,
                'details': details,
                'affiliate_commission': 0
            }
            
            response = requests.post(self.smm_api_url, data=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"SMM API response: {result}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"SMM API request error: {e}")
            return {"status": "fail", "error": f"API request failed: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"SMM API JSON decode error: {e}")
            return {"status": "fail", "error": "Invalid API response"}
        except Exception as e:
            logger.error(f"SMM API unexpected error: {e}")
            return {"status": "fail", "error": f"Unexpected error: {str(e)}"}

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        keyboard = [
            [InlineKeyboardButton("💰 Add Funds", callback_data='add_funds')],
            [InlineKeyboardButton("📊 Check Payment", callback_data='check_payment')],
            [InlineKeyboardButton("📜 Payment History", callback_data='payment_history')],
            [InlineKeyboardButton("ℹ️ Help", callback_data='help')]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_text = f"""
🤖 *Welcome to Chhean-SMM Payment Bot*

💰 *Add funds to your chhean-smm.net account instantly!*

🌟 *Features:*
• Quick USDT payments via Binance
• Instant balance top-up
• Secure payment verification
• Real-time transaction tracking

🔗 *Website:* https://chhean-smm.net
💳 *Payment Method:* Binance USDT Transfer
        """
        
        await update.message.reply_text(
            welcome_text, 
            reply_markup=reply_markup, 
            parse_mode='Markdown'
        )

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        if query.data == 'add_funds':
            await self.start_add_funds(query, context)
        elif query.data == 'check_payment':
            await self.start_check_payment(query, context)
        elif query.data == 'payment_history':
            await self.show_payment_history(query, context)
        elif query.data == 'help':
            await self.show_help(query, context)

    async def start_add_funds(self, query, context: ContextTypes.DEFAULT_TYPE):
        """Start the add funds process"""
        await query.edit_message_text(
            "💰 *Add Funds to Your Account*\n\n"
            "📝 Please enter your username from chhean-smm.net:",
            parse_mode='Markdown'
        )
        context.user_data['waiting_for_username'] = True

    async def start_check_payment(self, query, context: ContextTypes.DEFAULT_TYPE):
        """Start payment check process"""
        await query.edit_message_text(
            "🔍 *Check Payment Status*\n\n"
            "📝 Please enter your Payment ID:",
            parse_mode='Markdown'
        )
        context.user_data['waiting_for_payment_id'] = True

    async def show_payment_history(self, query, context: ContextTypes.DEFAULT_TYPE):
        """Show user's payment history"""
        user_id = query.from_user.id
        payments = self.db.get_user_payments(user_id, 10)
        
        if payments:
            history_lines = []
            for payment in payments:
                status_icon = "✅" if payment['status'] == 'completed' else "⏳" if payment['status'] == 'pending' else "❌"
                history_lines.append(
                    f"{status_icon} `{payment['payment_id']}` - ${payment['amount']} ({payment['status'].title()})"
                )
            history_text = "📜 *Your Payment History:*\n\n" + "\n".join(history_lines)
        else:
            history_text = "📜 *Payment History*\n\nNo payments found."
        
        await query.edit_message_text(history_text, parse_mode='Markdown')

    async def show_help(self, query, context: ContextTypes.DEFAULT_TYPE):
        """Show help information"""
        help_text = f"""
ℹ️ *How to Add Funds*

1️⃣ Click "Add Funds" button
2️⃣ Enter your chhean-smm.net username
3️⃣ Enter payment amount (min: $0.01)
4️⃣ Get Binance payment details & QR code
5️⃣ Send USDT to Binance ID: `{self.binance_id}`
6️⃣ Send the exact amount shown
7️⃣ Send payment confirmation (Order ID)
8️⃣ Funds added to your account automatically!

💡 *Tips:*
• Minimum payment: $0.01 USDT
• Maximum payment: $10,000 USDT
• Send the exact amount for verification
• No memo required - just the correct amount
• Keep your Order ID for verification
• Contact support if payment not processed

🆔 *Binance ID:* `{self.binance_id}`
🌐 *Website:* https://chhean-smm.net
        """
        
        await query.edit_message_text(help_text, parse_mode='Markdown')

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        user_data = context.user_data
        
        if user_data.get('waiting_for_username'):
            username = update.message.text.strip()
            if len(username) < 3:
                await update.message.reply_text(
                    "❌ *Username too short*\n\n"
                    "Please enter a valid username (minimum 3 characters):",
                    parse_mode='Markdown'
                )
                return
                
            user_data['username'] = username
            user_data['waiting_for_username'] = False
            await self.ask_for_amount(update, username)
            context.user_data['waiting_for_amount'] = True
            
        elif user_data.get('waiting_for_amount'):
            try:
                amount = float(update.message.text.strip())
                if amount < 0.01:
                    await update.message.reply_text(
                        "❌ *Minimum amount is $0.01 USDT*\n\n"
                        "Please enter an amount of $0.01 or higher:",
                        parse_mode='Markdown'
                    )
                    return
                elif amount > 10000:
                    await update.message.reply_text(
                        "❌ *Maximum amount is $10,000 USDT*\n\n"
                        "Please enter an amount of $10,000 or lower:",
                        parse_mode='Markdown'
                    )
                    return
                
                username = user_data['username']
                payment_id = await self.create_payment_request(update, context, username, amount)
                user_data['waiting_for_amount'] = False
                user_data['waiting_for_order_id'] = True
                user_data['current_payment_id'] = payment_id
                
            except ValueError:
                await update.message.reply_text(
                    "❌ *Invalid amount format*\n\n"
                    "Please enter a valid number (e.g., 0.01, 15.50, 100):\n\n"
                    "• Minimum: $0.01 USDT\n"
                    "• Maximum: $10,000 USDT\n"
                    "• Use numbers only (no $ symbol)",
                    parse_mode='Markdown'
                )
                
        elif user_data.get('waiting_for_payment_id'):
            payment_id = update.message.text.strip().upper()
            await self.check_payment_status(update, payment_id)
            user_data['waiting_for_payment_id'] = False
            
        elif user_data.get('waiting_for_order_id'):
            order_id = update.message.text.strip()
            payment_id = user_data.get('current_payment_id')
            await self.verify_payment(update, context, payment_id, order_id)
            user_data['waiting_for_order_id'] = False

    async def ask_for_amount(self, update, username):
        """Ask user to input the payment amount"""
        amount_text = f"""
💰 *Enter Payment Amount*

👤 Username: `{username}`
🌐 Website: chhean-smm.net

💡 *Enter the amount you want to add (in USDT):*

📝 *Examples:*
• `0.01` = $0.01 USDT
• `0.50` = $0.50 USDT  
• `15.75` = $15.75 USDT
• `250` = $250 USDT

📏 *Limits:*
• Minimum: $0.01 USDT
• Maximum: $10,000 USDT

⚠️ *Note:* Enter numbers only (no $ symbol)
        """
        
        await update.message.reply_text(amount_text, parse_mode='Markdown')

    async def create_payment_request(self, update, context, username, amount):
        """Create payment request from message"""
        payment_id = self.generate_payment_id()
        
        payment_data = {
            'payment_id': payment_id,
            'username': username,
            'amount': amount,
            'user_id': update.effective_user.id,
            'user_username': update.effective_user.username,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=2)).isoformat()
        }
        
        # Save to database
        self.db.save_payment(payment_data)
        
        # Generate payment QR code
        qr_buffer = await self.get_binance_pay_qr_code(amount, payment_id)
        
        # Create stylish all-in-one payment message
        payment_text = f"""
╔══════════════════════════╗
║    🎯 **PAYMENT CREATED**    ║
╚══════════════════════════╝

🆔 **Payment ID:** `{payment_id}`
👤 **Username:** `{username}`
💰 **Amount:** `${amount} USDT`
⏰ **Expires:** 2 hours

┌─────────────────────────┐
│     💳 **PAYMENT DETAILS**     │
└─────────────────────────┘

💰 **Amount:** `${amount} USDT`
🆔 **Binance ID:** `{self.binance_id}`

📱 **Instructions:**
1️⃣ **Scan QR code** below to see payment details
2️⃣ **Open Binance app** → Go to "Pay" section
3️⃣ **Send to Binance Pay ID:** `{self.binance_id}`
4️⃣ **Amount:** Exactly `${amount} USDT`
5️⃣ **Copy Order ID** after successful payment
6️⃣ **Send Order ID** to this bot for verification

💡 **QR Code contains:** Payment details and instructions
⚠️ **Important:** Use exact amount `${amount} USDT`
⚡ **Ready to pay? Let's go!** 🚀
        """
        
        # Send the stylish payment instructions with QR code
        if qr_buffer:
            await update.message.reply_photo(
                photo=qr_buffer,
                caption=payment_text,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(payment_text, parse_mode='Markdown')
        
        # Simple instruction for Order ID
        await update.message.reply_text(
            "📤 **Send your Binance Order ID:**",
            parse_mode='Markdown'
        )
        
        return payment_id

    async def verify_payment(self, update, context, payment_id, order_id):
        """Verify payment with Order ID (with duplicate prevention)"""
        try:
            # Get payment from database
            payment_data = self.db.get_payment(payment_id)
            if not payment_data:
                await update.message.reply_text("❌ Payment ID not found or already processed.")
                return
            
            # Check if payment is already completed
            if payment_data['status'] == 'completed':
                await update.message.reply_text(
                    f"✅ This payment has already been processed.\n\n"
                    f"🆔 Payment ID: `{payment_id}`\n"
                    f"💰 Amount: ${payment_data['amount']} USDT\n"
                    f"👤 Username: {payment_data['username']}\n"
                    f"📤 Order ID: `{payment_data['order_id']}`",
                    parse_mode='Markdown'
                )
                return
            
            await update.message.reply_text("🔍 Processing your payment... Please wait.")
            
            # Validate Order ID format and verify with Binance
            if not order_id or len(order_id.strip()) < 5:
                await update.message.reply_text(
                    "❌ Invalid Order ID. Please provide a valid Binance transaction ID.\n\n"
                    "📝 Examples of valid IDs:\n"
                    "• Order ID: 123456789\n"
                    "• Transaction ID: TXN123456789\n"
                    "• Deposit ID: DEP987654321\n\n"
                    "💡 You can find these in your Binance transaction history."
                )
                return

            # Verify the transaction with Binance API
            await update.message.reply_text("🔍 Verifying transaction with Binance... Please wait.")

            verification_result = await self.verify_binance_transaction(
                order_id,
                payment_data['amount'],
                'USDT'
            )

            if not verification_result['valid']:
                await update.message.reply_text(
                    f"❌ Transaction verification failed!\n\n"
                    f"🚫 Error: {verification_result['error']}\n\n"
                    f"💡 Please ensure you:\n"
                    f"• Provide a real transaction ID from your Binance account\n"
                    f"• The transaction amount matches ${payment_data['amount']} USDT\n"
                    f"• The transaction is recent (within 24 hours)\n\n"
                    f"📱 Check your Binance app → Wallet → Transaction History",
                    parse_mode='Markdown'
                )
                return

            # Transaction verified successfully
            await update.message.reply_text(
                f"✅ Transaction verified successfully!\n\n"
                f"🔍 Transaction Details:\n"
                f"• Type: {verification_result['transaction_type'].title()}\n"
                f"• Amount: {verification_result['amount']} {verification_result['asset']}\n"
                f"• ID: `{order_id}`\n\n"
                f"Processing payment...",
                parse_mode='Markdown'
            )
            
            # Check if this order ID was already used (duplicate prevention)
            if self.db.is_order_used(order_id):
                await update.message.reply_text(
                    f"❌ Order ID `{order_id}` has already been used.\n\n"
                    f"🔍 This Order ID was previously used for another payment.\n\n"
                    "Please provide a unique Order ID for this payment.",
                    parse_mode='Markdown'
                )
                return
            
            # Check payment timing (should be recent)
            created_time = datetime.fromisoformat(payment_data['created_at'])
            time_since_creation = (datetime.now() - created_time).total_seconds()
            
            if time_since_creation > 86400:  # 24 hours
                await update.message.reply_text(
                    "❌ Payment request has expired (older than 24 hours).\n\n"
                    "Please create a new payment request."
                )
                return
            
            # Process the payment
            await update.message.reply_text(
                f"✅ Order ID received: `{order_id}`\n"
                f"💰 Processing ${payment_data['amount']} USDT for {payment_data['username']}\n\n"
                f"Adding funds to your account..."
            )
            
            # Add funds via SMM API
            smm_result = await self.add_funds_to_smm(
                payment_data['username'],
                payment_data['amount'],
                f"Binance payment - ID: {payment_id}, Order: {order_id}, User: {payment_data['username']}"
            )
            
            if smm_result.get('status') == 'success':
                # Mark order as used first
                self.db.mark_order_used(order_id, payment_id, update.effective_user.id)
                
                # Update payment status in database
                self.db.update_payment_status(
                    payment_id, 
                    'completed', 
                    order_id, 
                    datetime.now().isoformat(), 
                    smm_result
                )
                
                # Success message to user
                success_text = f"""
🎉 *Payment Successful!*

✅ Payment processed successfully
💰 Amount: ${payment_data['amount']} USDT
👤 Username: {payment_data['username']}
🆔 Payment ID: `{payment_id}`
📤 Order ID: `{order_id}`

💳 Funds have been added to your chhean-smm.net account!

🌐 Login to check your balance: https://chhean-smm.net
                """
                
                await update.message.reply_text(success_text, parse_mode='Markdown')
                
                # Send high-quality log to group
                log_message = f"""
╔═══════════════════════════╗
║      💰 **PAYMENT SUCCESS**      ║
╚═══════════════════════════╝

┌─────────── TRANSACTION ───────────┐
🆔 **Payment ID:** `{payment_id}`
👤 **Username:** `{payment_data['username']}`
💵 **Amount:** `${payment_data['amount']} USDT`
📤 **Order ID:** `{order_id}`
└─────────────────────────────────┘

┌──────────── USER INFO ────────────┐
👥 **Telegram:** @{update.effective_user.username or 'Anonymous'}
🔢 **User ID:** `{update.effective_user.id}`
📅 **Time:** `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`
└─────────────────────────────────┘

✅ **Status:** Funds successfully added to account
🌐 **Platform:** chhean-smm.net
⚡ **Processing:** Automated via bot

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎉 **Another satisfied customer!** 🚀
                """
                
                await self.send_log_to_group(context, log_message)
                
            else:
                error_message = f"❌ Failed to add funds to your account.\n" \
                               f"Error: {smm_result.get('error', 'Unknown error')}\n\n" \
                               f"Please contact support with:\n" \
                               f"• Payment ID: `{payment_id}`\n" \
                               f"• Order ID: `{order_id}`\n" \
                               f"• Username: {payment_data['username']}"
                
                await update.message.reply_text(error_message, parse_mode='Markdown')
                
                # Send high-quality error log to group
                error_log = f"""
╔═══════════════════════════╗
║     ❌ **PAYMENT FAILED**      ║
╚═══════════════════════════╝

┌─────────── TRANSACTION ───────────┐
🆔 **Payment ID:** `{payment_id}`
👤 **Username:** `{payment_data['username']}`
💵 **Amount:** `${payment_data['amount']} USDT`
📤 **Order ID:** `{order_id}`
└─────────────────────────────────┘

┌──────────── USER INFO ────────────┐
👥 **Telegram:** @{update.effective_user.username or 'Anonymous'}
🔢 **User ID:** `{update.effective_user.id}`
📅 **Time:** `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`
└─────────────────────────────────┘

❌ **Error:** `{smm_result.get('error', 'Unknown error')}`
⚠️ **Status:** Manual verification required
🔧 **Action:** Admin intervention needed

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚨 **Requires immediate attention!**
                """
                
                await self.send_log_to_group(context, error_log)
                
        except Exception as e:
            logger.error(f"Payment verification error: {e}")
            await update.message.reply_text(
                f"❌ Processing error: {str(e)}\n\n"
                f"Please contact support with:\n"
                f"• Payment ID: `{payment_id}`\n"
                f"• Order ID: `{order_id}`",
                parse_mode='Markdown'
            )

    async def check_payment_status(self, update, payment_id):
        """Check payment status by ID"""
        payment = self.db.get_payment(payment_id)
        
        if payment:
            if payment['status'] == 'pending':
                status_text = f"""
📋 *Payment Status: PENDING*

🆔 Payment ID: `{payment_id}`
👤 Username: {payment['username']}
💰 Amount: ${payment['amount']} USDT
📅 Created: {payment['created_at'][:19]}
⏰ Expires: {payment['expires_at'][:19]}

🔸 Waiting for payment confirmation
🔸 Send your Binance Order ID when ready
                """
            elif payment['status'] == 'completed':
                status_text = f"""
📋 *Payment Status: COMPLETED* ✅

🆔 Payment ID: `{payment_id}`
👤 Username: {payment['username']}
💰 Amount: ${payment['amount']} USDT
📅 Created: {payment['created_at'][:19]}
✅ Completed: {payment['completed_at'][:19]}
📤 Order ID: `{payment.get('order_id', 'N/A')}`

🎉 Funds successfully added to account!
                """
            elif payment['status'] == 'expired':
                status_text = f"""
📋 *Payment Status: EXPIRED* ❌

🆔 Payment ID: `{payment_id}`
👤 Username: {payment['username']}
💰 Amount: ${payment['amount']} USDT
📅 Created: {payment['created_at'][:19]}
❌ Expired: {payment['expires_at'][:19]}

🔄 Please create a new payment request
                """
            else:
                status_text = f"""
📋 *Payment Status: {payment['status'].upper()}*

🆔 Payment ID: `{payment_id}`
👤 Username: {payment['username']}
💰 Amount: ${payment['amount']} USDT
📅 Created: {payment['created_at'][:19]}
                """
        else:
            status_text = f"❌ Payment ID `{payment_id}` not found."
        
        await update.message.reply_text(status_text, parse_mode='Markdown')

    async def cleanup_expired_payments_task(self, context: ContextTypes.DEFAULT_TYPE):
        """Periodic task to clean up expired payments"""
        try:
            self.db.cleanup_expired_payments()
            logger.info("Cleaned up expired payments")
        except Exception as e:
            logger.error(f"Error cleaning up expired payments: {e}")

def main():
    """Main function to run the bot"""
    
    # Check if values have been replaced
    default_values = [
        "YOUR_TELEGRAM_BOT_TOKEN_HERE",
        "YOUR_BINANCE_API_KEY_HERE", 
        "YOUR_BINANCE_SECRET_KEY_HERE",
        "YOUR_SMM_API_KEY_HERE",
        "YOUR_BINANCE_ID_HERE"
    ]
    
    current_values = [TELEGRAM_TOKEN, BINANCE_API_KEY, BINANCE_SECRET_KEY, SMM_API_KEY, BINANCE_ID]
    
    if any(val in default_values for val in current_values):
        print("❌ Please replace the placeholder values with your actual API keys!")
        print("📍 Find the API configuration section at the TOP of the bot.py file")
        print("\n📝 What you need to replace:")
        print(f"TELEGRAM_TOKEN: {'❌ Update needed' if TELEGRAM_TOKEN in default_values else '✅ Updated'}")
        print(f"BINANCE_API_KEY: {'❌ Update needed' if BINANCE_API_KEY in default_values else '✅ Updated'}")
        print(f"BINANCE_SECRET_KEY: {'❌ Update needed' if BINANCE_SECRET_KEY in default_values else '✅ Updated'}")
        print(f"SMM_API_KEY: {'❌ Update needed' if SMM_API_KEY in default_values else '✅ Updated'}")
        print(f"BINANCE_ID: {'❌ Update needed' if BINANCE_ID in default_values else '✅ Updated'}")
        print("\n🔗 Where to get your API keys:")
        print("• Telegram: @BotFather")
        print("• Binance: https://www.binance.com/en/my/settings/api-management")
        print("• SMM Panel: Your chhean-smm.net admin panel")
        print("• Binance ID: Binance Profile > Security")
        print("• Log Group ID: Add bot to group, use @userinfobot to get group ID")
        return
    
    if not all([TELEGRAM_TOKEN, BINANCE_API_KEY, BINANCE_SECRET_KEY, SMM_API_KEY, BINANCE_ID]):
        print("❌ One or more API keys are empty. Please check your configuration.")
        return
    
    # Initialize bot
    payment_bot = SMMBinancePaymentBot(
        TELEGRAM_TOKEN, 
        BINANCE_API_KEY, 
        BINANCE_SECRET_KEY, 
        SMM_API_KEY,
        BINANCE_ID,
        LOG_GROUP_ID
    )
    
    # Create application
    application = Application.builder().token(TELEGRAM_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", payment_bot.start))
    application.add_handler(CallbackQueryHandler(payment_bot.button_callback))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, payment_bot.handle_message))
    
    # Add periodic cleanup job (every hour)
    job_queue = application.job_queue
    job_queue.run_repeating(payment_bot.cleanup_expired_payments_task, interval=3600, first=60)
    
    print("🚀 Starting Enhanced SMM Binance Payment Bot...")
    print(f"🗄️ Storage: In-Memory (No Database)")
    print(f"🆔 Binance ID: {BINANCE_ID}")
    print(f"🌐 SMM API: https://chhean-smm.net/adminapi/v1")
    print(f"📢 Log Group: {LOG_GROUP_ID if LOG_GROUP_ID != '-*************' else 'Not configured'}")
    print("✅ Enhanced features: In-memory storage, duplicate prevention, group logging")
    
    application.run_polling()

if __name__ == '__main__':
    main()